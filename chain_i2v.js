// chain_i2v.js
// Node 18+
// Usage: node chain_i2v.js ./config/config.json

import fs from "fs";
import fsp from "fs/promises";
import path from "path";
import { exec as _exec } from "child_process";
import { fileURLToPath } from "url";
import axios from "axios";
import FormData from "form-data";
import dotenv from "dotenv";
import { LLMServiceFactory, SimpleInterpolationService } from "./llm-service.js";

// Load environment variables from .env file
dotenv.config();
const exec = (cmd) =>
  new Promise((resolve, reject) =>
    _exec(cmd, (err, stdout, stderr) => (err ? reject(new Error(stderr || err.message)) : resolve({ stdout, stderr })))
  );

let WebSocket = null;
try { ({ default: WebSocket } = await import("ws")); } catch { /* optional */ }

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ---- config ----
const COMFY_BASE = process.env.COMFY_BASE || "http://127.0.0.1:8188";
const BASE_WORKFLOW_PATH = process.env.WORKFLOW_JSON || path.join(__dirname, "wan2.2_chain_base.json"); // put your uploaded file name here
const WORK_DIR = path.join(__dirname, "runs");
// API keys are now handled by the LLM service factory

async function waitForHistory(promptId, { timeoutMs = 10 * 60 * 1000, pollMs = 1500 } = {}) {
  const started = Date.now();
  const spinner = ["|","/","-","\\"];
  let spinIdx = 0;
  let ws = null;
  let wsMsg = ""; // last progress line from WS

  // optional websocket live progress
  if (WebSocket) {
    try {
      ws = new WebSocket(COMFY_BASE.replace("http", "ws") + "/ws");
      ws.on("open", () => {
        // no handshake needed - ComfyUI streams all job events
      });
      ws.on("message", (buf) => {
        try {
          const msg = JSON.parse(buf.toString());
          // common messages: { type: "status", data: {...} }, { type: "progress", data:{ node_id, value, max } }, { type:"executing", data:{ node:{ id, type }, prompt_id } }
          if (msg?.type === "progress" && msg?.data?.max) {
            const pct = Math.floor((msg.data.value / msg.data.max) * 100);
            wsMsg = `progress: node ${msg.data.node_id} ${pct}%`;
          } else if (msg?.type === "executing" && msg?.data?.prompt_id === promptId) {
            const n = msg.data.node;
            wsMsg = `executing: ${n?.type || "node"} (${n?.id ?? "?"})`;
          } else if (msg?.type === "status") {
            // you could read queue/running counts here if you want
          }
        } catch { /* ignore malformed */ }
      });
      ws.on("error", () => {});
    } catch { ws = null; }
  }

  // render loop
  process.stdout.write("\n");
  while (true) {
    const res = await axios.get(`${COMFY_BASE}/history/${promptId}`).catch(() => ({ data: null }));
    const h = res.data?.[promptId];

    if (h?.status?.completed === true) {
      process.stdout.write(`\r✔ ComfyUI done in ${Math.round((Date.now()-started)/1000)}s${" ".repeat(40)}\n`);
      if (ws) try { ws.close(); } catch {}
      return h;
    }
    if (h?.status?.status_str === "error") {
      if (ws) try { ws.close(); } catch {}
      throw new Error(`ComfyUI job failed: ${JSON.stringify(h.status)}`);
    }

    const elapsed = Math.round((Date.now() - started) / 1000);
    const spin = spinner[spinIdx++ % spinner.length];
    const line = wsMsg ? `waiting ${spin}  ${wsMsg}  elapsed ${elapsed}s` : `waiting ${spin}  elapsed ${elapsed}s`;
    process.stdout.write(`\r${line}${" ".repeat(30)}`);

    if (Date.now() - started > timeoutMs) {
      if (ws) try { ws.close(); } catch {}
      throw new Error("Timed out waiting for ComfyUI history");
    }
    await sleep(pollMs);
  }
}


// utility
const sleep = (ms) => new Promise((r) => setTimeout(r, ms));
const ensureDir = async (p) => fsp.mkdir(p, { recursive: true });

async function uploadImageToComfy(localPath) {
  const form = new FormData();
  form.append("image", fs.createReadStream(localPath));
  // saves into ComfyUI/input
  const url = `${COMFY_BASE}/upload/image`;
  const res = await axios.post(url, form, { headers: form.getHeaders(), maxContentLength: Infinity, maxBodyLength: Infinity });
  // Response usually { name: "filename.png", subfolder: "", type: "input" }
  const name = res?.data?.name || path.basename(localPath);
  return name;
}

async function postPrompt(graph) {
  const res = await axios.post(`${COMFY_BASE}/prompt`, { prompt: graph });
  // returns { prompt_id, number }
  return res.data.prompt_id;
}

async function __waitForHistory(promptId, { timeoutMs = 10 * 60 * 1000, pollMs = 1500 } = {}) {
  const started = Date.now();
  while (true) {
    const res = await axios.get(`${COMFY_BASE}/history/${promptId}`);
    const h = res.data?.[promptId];
    if (h && h.status?.completed === true) return h;
    if (h && h.status?.status_str === "error") throw new Error(`ComfyUI job failed: ${JSON.stringify(h.status)}`);
    if (Date.now() - started > timeoutMs) throw new Error("Timed out waiting for ComfyUI history");
    await sleep(pollMs);
  }
}

function findVHSOutput(historyObj) {
  // scan outputs for any node that produced a video file
  // historyObj.outputs is keyed by node_id strings ("8", "23", etc.)
  for (const [nodeId, out] of Object.entries(historyObj.outputs || {})) {
    const files = out.files || out.images || out.gifs || [];
    // Comfy returns { filename, subfolder, type } for each file
    const video = files.find((f) => typeof f.filename === "string" && f.filename.toLowerCase().endsWith(".mp4"));
    if (video) return video;
  }
  return null;
}

async function downloadComfyFile(fileInfo, destPath) {
  // fileInfo: { filename, subfolder, type: "output" }
  const params = new URLSearchParams({
    filename: fileInfo.filename,
    subfolder: fileInfo.subfolder || "",
    folder: fileInfo.type || "output",
  });
  const url = `${COMFY_BASE}/view?${params.toString()}`;
  const res = await axios.get(url, { responseType: "arraybuffer" });
  await fsp.writeFile(destPath, Buffer.from(res.data));
}

function framesFromSeconds(seconds, fps) {
  return Math.max(1, Math.round(Number(seconds) * Number(fps)));
}

async function extractLastFrame(inVideoPath, outImagePath) {
  // relies on ffmpeg being installed
  // First get the total frame count, then extract the last frame (frame count - 1)
  const countCmd = `ffprobe -v error -select_streams v:0 -count_frames -show_entries stream=nb_frames -of csv=p=0 "${inVideoPath}"`;
  const { stdout: frameCountStr } = await exec(countCmd);
  const frameCount = parseInt(frameCountStr.trim());
  const lastFrameIndex = frameCount - 1;

  // Extract the specific last frame
  const cmd = `ffmpeg -y -i "${inVideoPath}" -vf "select='eq(n\\,${lastFrameIndex})'" -vframes 1 "${outImagePath}"`;
  await exec(cmd);
}

async function concatVideosLossless(segmentPaths, outputPath) {
  const listFile = path.join(path.dirname(outputPath), "concat_list.txt");
  const listText = segmentPaths.map((p) => `file '${p.replace(/'/g, "'\\''")}'`).join("\n");
  await fsp.writeFile(listFile, listText);
  // Use stream copy to keep original quality
  const cmd = `ffmpeg -y -f concat -safe 0 -i "${listFile}" -c copy "${outputPath}"`;
  await exec(cmd);
}

async function patchGraphForSegment(baseGraphJson, {
  inputImageName,
  width,
  height,
  fps,
  frames,
  positivePrompt,
  negativePrompt,
  filenamePrefix,
  seed,
  interpolateMode = "none",
  promptChain = [],
  segmentIndex = 0,
  llmInterpolation = false,
  llmService = null,
  simpleInterpolationService = null,
}) {
  // Helper function to escape JSON string values
  function escapeJsonString(str) {
    if (str == null) return "";
    return String(str)
      .replace(/\\/g, "\\\\")
      .replace(/"/g, '\\"')
      .replace(/\n/g, "\\n")
      .replace(/\r/g, "\\r")
      .replace(/\t/g, "\\t");
  }

  // Note: seed should already be resolved to a specific value by the caller
  // No random generation here - seed is passed as a resolved value

  // Handle prompt interpolation based on mode
  let finalPositivePrompt = positivePrompt;

  if (interpolateMode && interpolateMode !== "none" && promptChain && promptChain.length > 1) {
    const isFirstSegment = segmentIndex === 0;
    const isLastSegment = segmentIndex === promptChain.length - 1;
    const previousPrompt = !isFirstSegment ? promptChain[segmentIndex - 1]?.positive_prompt : null;
    const nextPrompt = !isLastSegment ? promptChain[segmentIndex + 1]?.positive_prompt : null;

    var finalPositivePrompt_simple = generateSimpleInterpolation(positivePrompt, previousPrompt, nextPrompt, interpolateMode, simpleInterpolationService);

    // Check if we should use LLM enhancement
    if (llmInterpolation && llmService) {
      try {
        console.log(`    [LLM Enhancement] Generating enhanced prompt for ${interpolateMode} mode...`);
        finalPositivePrompt = await generateLLMPrompt(previousPrompt, positivePrompt, nextPrompt, interpolateMode, llmService);
        // finalPositivePrompt += "\n" + finalPositivePrompt_simple
        console.log(`    [LLM Enhancement] Original: "${positivePrompt}"`);
        console.log(`    [LLM Enhancement] Enhanced: "${finalPositivePrompt}"`);
      } catch (error) {
        console.log(`    [LLM Enhancement] Failed: ${error.message}`);
        console.log(`    [LLM Enhancement] Falling back to simple interpolation...`);

        // Fall back to simple interpolation
        finalPositivePrompt = generateSimpleInterpolation(positivePrompt, previousPrompt, nextPrompt, interpolateMode, simpleInterpolationService);
      }
    } else {
      // Use simple interpolation
      finalPositivePrompt = finalPositivePrompt_simple;
    }
    
    console.log(`\n[Final Prompt] ${finalPositivePrompt}\n`);
  }

  // Helper function for simple interpolation using the new service
  function generateSimpleInterpolation(currentPrompt, prevPrompt, nextPrompt, mode, interpolationService) {
    const result = interpolationService.generateInterpolation(currentPrompt, prevPrompt, nextPrompt, mode);

    // Log simple interpolation if prompt was modified
    if (result !== currentPrompt) {
      console.log(`    [Simple Interpolation ${mode}] Original: "${currentPrompt}"`);
      console.log(`    [Simple Interpolation ${mode}] Enhanced: "${result}"`);
    }

    return result;
  }

  // Replace placeholders in the JSON string
  let patchedJson = baseGraphJson
    .replace(/"{{negative_prompt}}"/g, `"${escapeJsonString(negativePrompt || "")}"`)
    .replace(/"{{positive_prompt}}"/g, `"${escapeJsonString(finalPositivePrompt || "")}"`)
    .replace(/"{{width}}"/g, String(Number(width)))
    .replace(/"{{height}}"/g, String(Number(height)))
    .replace(/"{{frames}}"/g, String(Number(frames)))
    .replace(/"{{fps}}"/g, String(Number(fps)))
    .replace(/"{{filename_prefix}}"/g, `"${escapeJsonString(filenamePrefix)}"`)
    .replace(/"{{input_image}}"/g, `"${escapeJsonString(inputImageName)}"`)
    .replace(/"{{seed}}"/g, String(Number(seed)));

  // Parse the patched JSON
  const graph = JSON.parse(patchedJson);

  return graph;
}

function randomSeed() {
  const min = 1000000000;
  const max = 9999999999;
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

async function generateLLMPrompt(previousPrompt, currentPrompt, nextPrompt, interpolateMode, llmService) {
  if (!llmService) {
    throw new Error("LLM service not initialized");
  }

  return await llmService.generatePrompt(previousPrompt, currentPrompt, nextPrompt, interpolateMode);
}

async function main() {
  const cfgPath = process.argv[2];
  if (!cfgPath) {
    console.error("Usage: node chain_i2v.js ./config/config.json");
    process.exit(1);
  }

  await ensureDir(WORK_DIR);

  const cfgRaw = await fsp.readFile(cfgPath, "utf-8");
  const cfg = JSON.parse(cfgRaw);

  const {
    image,
    width,
    height,
    fps,
    seed = -1,
    prompt_chain = [],
    output = "final_output.mp4",
    segment_prefix = "segment",
    interpolate_prompts = "none",
    llm_interpolation = false,
    llm_config = {}
  } = cfg;

  // Set up default LLM configuration for backward compatibility
  const defaultLLMConfig = {
    service: "openai",
    model: "gpt-4o",
    temperature: 0.6,
    max_tokens: 100,
    api_base: "https://api.openai.com/v1/chat/completions",
    timeout: 30000,
    _system_prompt: "You are an expert in creating smooth video transitions for AI video generation. Your task is to enhance video prompts so that scenes flow seamlessly while maintaining consistency in visuals, characters, and style.\n\nGuidelines:\n- The video will start from a provided image, which defines the initial look and feel. Do not overdefine or contradict it.\n- Preserve the same characters, setting, and visual style.\n- Ensure smooth transitions by preparing for upcoming elements while preserving current ones.\n- Keep the core action and mood of the current scene intact.\n- Subtly introduce elements from the next scene (if provided).\n- Respect continuity from the previous scene (if provided).\n- Use clear, descriptive language suitable for AI video generation.\n- Return ONLY the enhanced prompt (no explanations or extra text).\n- Do not invent new elements outside the provided context.\n- Structure the prompt as: SCENE + ACTOR(S) + ACTION.\n- Avoid describing appearance details (such as colors, clothing, or specific looks) that are already defined by the starting image, to prevent conflicts.\n- Clearly describe how the scene begins (based on the previous scene) and how it ends (leading into the next). For example, if a character was moving in the previous scene, ensure the motion continues here.\n- Do not add new characters or elements that were not present in the previous scene, or described in the prompt ( current, previous, or next)\n- Do not remove characters or elements that were present in the current, or will be present in the next scene unless described in the prompt\n- add amphasis to [ACTION] that you prepare in the current prompt and are present in next one with ([ACTION]:1.5)\n\nContext:\n{{context}}\n\nGenerate a single enhanced prompt for the current scene that ensures smooth transitions:",
    _user_prompt: "Create an enhanced prompt for the current scene that maintains visual consistency and creates smooth transitions.",
    system_prompt: "You are an expert AI assistant that creates seamless, descriptive prompts for a continuous video generator. Your job is to take a series of video scene descriptions and generate a single, highly detailed prompt for an image-to-video model. The goal is to ensure visual continuity and smooth, natural motion between scenes.\n\nYour response must be a single, concise prompt string for the video model. Do not include any extra text, explanations, or formatting.\n\nInstructions for prompt creation:\n1.  **Scene Start:** Describe the beginning of the video, based on the last moments of the *previous* scene. Focus on the state of the characters and the environment. For example, 'A woman is already in motion, walking down a street.'\n2.  **Core Action:** Detail the main event or action of the *current* scene. Be specific about character movements and any new objects or changes in the environment.\n3.  **Scene End:** Describe the final moments of the video, setting up the transition into the *next* scene. This is where you introduce elements from the next prompt. For example, 'A hat begins to fall from the sky towards her head.'\n4.  **Consistency:** Maintain a consistent style, setting, and character appearance throughout the prompt. Do not add new characters or elements unless they are explicitly mentioned in the provided prompts.\n5.  **Emphasis:** Use emphasis tags like (word:1.5) on key elements, especially new actions or objects that are crucial for the next scene's setup. For example, `(a hat begins to fall:1.5)`.\n6.  **Avoid Redundancy:** Do not describe appearance details (like clothing colors or hairstyles) that are already established in the starting image or previous scenes. Focus only on motion and new elements.\n7.  **Final Structure:** Combine the elements into a single flowing sentence or two. The structure should be `[Start State] + [Core Action] + [End State with Transition Element]`.\n\nContext:\n{{context}}\n\nGenerate the prompt now:",
    user_prompt: "Create a single, enhanced prompt for the current scene that maintains visual consistency and creates smooth transitions.",
    prompt_templates: {
      previous_template: "(transitioning from: \"{{prompt}}\":0.5)",
      current_template: "({{prompt}})",
      next_template: "(transitioning to: \"{{prompt}}\":0.5)"
    }
  };

  // Merge user config with defaults
  const finalLLMConfig = { ...defaultLLMConfig, ...llm_config };

  // Initialize LLM service if needed
  let llmService = null;
  let simpleInterpolationService = null;

  if (llm_interpolation) {
    try {
      // Get the appropriate API key based on the service
      let apiKey;
      if (finalLLMConfig.service === 'openai') {
        apiKey = process.env.OPENAI_API_KEY;
      } else if (finalLLMConfig.service === 'openrouter') {
        apiKey = process.env.OPENROUTER_API_KEY;
      } else {
        // Fallback: try both keys
        apiKey = process.env.OPENAI_API_KEY || process.env.OPENROUTER_API_KEY;
      }

      llmService = LLMServiceFactory.create(finalLLMConfig, apiKey);
      console.log(`LLM service initialized: ${finalLLMConfig.service} with model ${finalLLMConfig.model}`);
    } catch (error) {
      console.log(`LLM service initialization failed: ${error.message}`);
      console.log("Falling back to simple interpolation only");
    }
  }

  // Initialize simple interpolation service
  simpleInterpolationService = new SimpleInterpolationService(finalLLMConfig.prompt_templates);

  if (!image) throw new Error("config.image is required");

  // Handle seed: if -1, generate random seed, otherwise use provided value
  // Note: Each segment will use a unique seed (baseSeed + segmentIndex) to prevent
  // duplicate frames between segments while maintaining reproducibility
  let resolvedSeed = seed;
  if (resolvedSeed === -1 || resolvedSeed === "-1") {
    resolvedSeed = randomSeed();
    console.log(`Generated base seed: ${resolvedSeed} (each segment will use base + index)`);
  } else {
    console.log(`Using provided base seed: ${resolvedSeed} (each segment will use base + index)`);
  }

  const baseGraphJson = await fsp.readFile(BASE_WORKFLOW_PATH, "utf-8");

  console.log("Uploading initial image:", image);
  console.log("Prompt interpolation mode:", interpolate_prompts);
  console.log("LLM interpolation:", llm_interpolation ? "enabled" : "disabled");

  // Show mode description
  const modeDescriptions = {
    "none": "No interpolation - prompts used as-is",
    "next": "Forward interpolation - adds next prompt to current segments",
    "previous": "Backward interpolation - adds previous prompt to current segments",
    "both": "Bidirectional interpolation - adds both previous and next prompts"
  };
  if (modeDescriptions[interpolate_prompts]) {
    console.log("Mode description:", modeDescriptions[interpolate_prompts]);
  }

  // Show LLM status
  if (llm_interpolation) {
    if (llmService) {
      console.log(`LLM enhancement: Ready (${finalLLMConfig.service} service with ${finalLLMConfig.model})`);
    } else {
      console.log("LLM enhancement: WARNING - Failed to initialize LLM service");
      console.log(`  Please check your .env file and ensure ${finalLLMConfig.service.toUpperCase()}_API_KEY is set`);
      console.log("  Copy .env.example to .env and add your API keys");
    }
  }

  const firstImageName = await uploadImageToComfy(path.resolve(image));
  let currentStartImageName = firstImageName;

  const segmentVideos = [];

  for (let i = 0; i < prompt_chain.length; i++) {
    const seg = prompt_chain[i];
    const seconds = Number(seg.length || 5);
    const frameCount = framesFromSeconds(seconds, fps);

    // Generate unique seed for each segment to avoid duplicate frames
    // Use base seed + segment index to maintain reproducibility
    const segmentSeed = resolvedSeed + i;

    console.log(`\n[Segment ${i + 1}/${prompt_chain.length}]`);
    console.log("  Positive prompt:", seg.positive_prompt);
    console.log("  Negative prompt:", seg.negative_prompt);
    console.log(`  Duration: ${seconds}s (${frameCount} frames @ ${fps} fps)`);
    console.log(`  Using seed: ${segmentSeed} (base: ${resolvedSeed} + offset: ${i})`);

    const filenamePrefix = `${segment_prefix}_${String(i + 1).padStart(2, "0")}`;

    const graph = await patchGraphForSegment(baseGraphJson, {
      inputImageName: currentStartImageName,
      width,
      height,
      fps,
      frames: frameCount,
      positivePrompt: seg.positive_prompt,
      negativePrompt: seg.negative_prompt,
      filenamePrefix,
      seed: segmentSeed,
      interpolateMode: interpolate_prompts,
      promptChain: prompt_chain,
      segmentIndex: i,
      llmInterpolation: llm_interpolation,
      llmService: llmService,
      simpleInterpolationService: simpleInterpolationService
    });

    console.log("  Submitting to ComfyUI...");
    const promptId = await postPrompt(graph);

    console.log(`  Prompt ID: ${promptId}`);
    console.log("  Waiting for ComfyUI (live progress below)...");

    console.log("  Waiting for ComfyUI to finish (this may take a while)...");
    const hist = await waitForHistory(promptId);

    console.log("  Downloading video...");
    const vfile = findVHSOutput(hist);
    const segPath = path.join(WORK_DIR, `${filenamePrefix}.mp4`);
    await downloadComfyFile(vfile, segPath);
    console.log("  Saved:", segPath);
    segmentVideos.push(segPath);

    if (i < prompt_chain.length - 1) {
      console.log("  Extracting last frame for next segment...");
      const lastFramePath = path.join(WORK_DIR, `${filenamePrefix}_last.png`);
      await extractLastFrame(segPath, lastFramePath);
      const uploadedName = await uploadImageToComfy(lastFramePath);
      currentStartImageName = uploadedName;
    }
  }

  console.log("\nMerging all segments...");

  // Generate timestamp and create filename with timestamp and seed
  const timestamp = Math.floor(Date.now() / 1000); // Unix timestamp
  const parsedPath = path.parse(output);
  const finalFilename = `${parsedPath.name}_${timestamp}_${resolvedSeed}${parsedPath.ext}`;
  const outPath = path.resolve(path.join(parsedPath.dir || '.', finalFilename));

  await concatVideosLossless(segmentVideos, outPath);

  console.log("\n✅ All done. Final video:", outPath);
}

main().catch((err) => {
  console.error("❌ Error:", err);
  process.exit(1);
});